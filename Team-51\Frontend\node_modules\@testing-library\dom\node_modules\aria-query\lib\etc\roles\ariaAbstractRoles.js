"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _commandRole = _interopRequireDefault(require("./abstract/commandRole"));
var _compositeRole = _interopRequireDefault(require("./abstract/compositeRole"));
var _inputRole = _interopRequireDefault(require("./abstract/inputRole"));
var _landmarkRole = _interopRequireDefault(require("./abstract/landmarkRole"));
var _rangeRole = _interopRequireDefault(require("./abstract/rangeRole"));
var _roletypeRole = _interopRequireDefault(require("./abstract/roletypeRole"));
var _sectionRole = _interopRequireDefault(require("./abstract/sectionRole"));
var _sectionheadRole = _interopRequireDefault(require("./abstract/sectionheadRole"));
var _selectRole = _interopRequireDefault(require("./abstract/selectRole"));
var _structureRole = _interopRequireDefault(require("./abstract/structureRole"));
var _widgetRole = _interopRequireDefault(require("./abstract/widgetRole"));
var _windowRole = _interopRequireDefault(require("./abstract/windowRole"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

var ariaAbstractRoles = [['command', _commandRole.default], ['composite', _compositeRole.default], ['input', _inputRole.default], ['landmark', _landmarkRole.default], ['range', _rangeRole.default], ['roletype', _roletypeRole.default], ['section', _sectionRole.default], ['sectionhead', _sectionheadRole.default], ['select', _selectRole.default], ['structure', _structureRole.default], ['widget', _widgetRole.default], ['window', _windowRole.default]];
var _default = ariaAbstractRoles;
exports.default = _default;