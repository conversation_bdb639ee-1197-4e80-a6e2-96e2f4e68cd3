{"name": "tr46", "version": "1.0.1", "description": "An implementation of the Unicode TR46 spec", "main": "index.js", "files": ["index.js", "lib/mappingTable.json", "lib/regexes.js"], "scripts": {"test": "mocha", "lint": "eslint .", "pretest": "node scripts/getLatestTests.js", "prepublish": "node scripts/generateMappingTable.js && node scripts/generateRegexes.js"}, "repository": "Sebmaster/tr46.js", "keywords": ["unicode", "tr46", "url", "whatwg"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"punycode": "^2.1.0"}, "devDependencies": {"eslint": "^3.13.0", "mocha": "^3.2.0", "regenerate": "^1.3.2", "request": "^2.79.0", "unicode-10.0.0": "^0.7.4"}, "unicodeVersion": "10.0.0"}